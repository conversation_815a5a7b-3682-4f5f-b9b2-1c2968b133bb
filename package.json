{"name": "elephantislandtemplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p3003 --experimental-https", "build": "next build", "start": "next start -p3003", "lint": "next lint", "verify-auth": "node scripts/verify-auth-config.js", "test-auth": "npm run verify-auth && echo 'Auth configuration verified!'", "test-middleware": "node scripts/test-auth-middleware.js", "test-auth-endpoints": "node scripts/test-auth-endpoints.js", "test-auth-full": "npm run verify-auth && npm run test-auth-endpoints", "test-production": "node scripts/test-production-api.js", "test-local": "node scripts/test-production-api.js --local"}, "dependencies": {"@heroicons/react": "^2.2.0", "@react-google-maps/api": "^2.20.7", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.7.0", "@types/three": "^0.176.0", "@vis.gl/react-google-maps": "^1.5.4", "clsx": "^2.1.1", "firebase": "^10.13.0", "firebase-admin": "^12.3.1", "framer-motion": "^12.23.2", "leva": "^0.10.0", "lil-gui": "^0.20.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "15.3.2", "nodemailer": "^7.0.3", "or": "^0.2.0", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "resend": "^4.0.0", "stripe": "^16.12.0", "tailwind-merge": "^3.3.0", "three": "^0.177.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}